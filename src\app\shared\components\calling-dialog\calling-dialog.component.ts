import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CallTypesEnum } from 'src/app/core/enums/call-types-enum.enum';
import { IUser } from 'src/app/core/interfaces/auth-interfaces/iuser-model';
import { IStartCallSessionRequest } from 'src/app/core/interfaces/calls/istart-call-session-request';
import { IProgTasmeeaTskCallConnector } from 'src/app/core/interfaces/calls/prog-tasks/iprog-tasmeea-tsk-call-connector';
import { BaseResponseModel } from 'src/app/core/ng-model/base-response-model';
import { CallsService } from 'src/app/core/services/calls-services/calls.service';
import { RingtoneService } from 'src/app/core/services/ringtone-services/ringtone.service';
import { SocketService } from 'src/app/core/services/socket-services/socket.service';

@Component({
  selector: 'app-calling-dialog',
  templateUrl: './calling-dialog.component.html',
  styleUrls: ['./calling-dialog.component.scss'],
})
export class CallingDialogComponent implements OnInit {
  currentUser: IUser | undefined;

  constructor(
    public dialogRef: MatDialogRef<CallingDialogComponent>,
    private callService: CallsService,
    private ringtoneService: RingtoneService,
    private socketService: SocketService,
    @Inject(MAT_DIALOG_DATA)
    public data: { callData: IProgTasmeeaTskCallConnector; role: any }
  ) {}

  ngOnInit(): void {
    this.currentUser = JSON.parse(localStorage.getItem('user') || '{}');
    this.startCall();
  }

  startCall(): void {
    const techInfo = this.data.callData;
    debugger;
    const payload: IStartCallSessionRequest = {
      modrId: this.currentUser?.id,
      toUserId: techInfo.availableTechInfo?.usrId,
      callModuleTypehuffazId: this.data.role,
      dayId: techInfo.tasmeeaTskFullDetails?.dayTask,
      tskId: techInfo.tasmeeaTskFullDetails?.id,
      callModuleEntityId: techInfo.tasmeeaTskFullDetails?.id,
      isVideoMute: techInfo.isVideoMute,
      batId: techInfo.batId,
    };
    this.ringtoneService.play();
    this.callService.startCallSession(payload).subscribe(
      (res: BaseResponseModel) => {
        debugger;
        if (res.data) {
          console.log('Call started successfully', res);
          this.ringtoneService.stop();
          this.socketService.emit('joinRoom', res.data.socketRoom);
        } else this.cancelCall();
      },
      (error) => {
        console.error('Error starting call:', error);
        this.cancelCall();
      }
    );
  }

  cancelCall(): void {
    this.dialogRef.close('cancelled');
    this.ringtoneService.stop();
  }
}
